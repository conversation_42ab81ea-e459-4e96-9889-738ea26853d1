use std::fs::File;
use std::io::BufReader;
use struson::reader::JsonStreamReader;

/// Create a JsonStreamReader with settings optimized for DuckDB JSON extension
pub fn create_json_reader(reader: <PERSON>ufReader<File>) -> JsonStreamReader<BufReader<File>> {
    let settings = struson::reader::ReaderSettings {
        allow_comments: false,
        allow_trailing_comma: false,
        restrict_number_values: false,
        allow_multiple_top_level: false,
        max_nesting_depth: Some(128),
        track_path: false,
    };
    JsonStreamReader::new_custom(reader, settings)
}

/// Helper function to create a JSON reader from string for testing
#[cfg(test)]
pub fn create_test_json_reader(json_str: &str) -> JsonStreamReader<std::io::BufReader<std::io::Cursor<Vec<u8>>>> {
    let cursor = std::io::Cursor::new(json_str.as_bytes().to_vec());
    let buf_reader = std::io::BufReader::new(cursor);
    let settings = struson::reader::ReaderSettings {
        allow_comments: false,
        allow_trailing_comma: false,
        restrict_number_values: false,
        allow_multiple_top_level: false,
        max_nesting_depth: Some(128),
        track_path: false,
    };
    JsonStreamReader::new_custom(buf_reader, settings)
}
