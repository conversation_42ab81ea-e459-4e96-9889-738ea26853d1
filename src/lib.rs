// Modular DuckDB JSON Extension
// Organized into logical modules for easier maintenance and refactoring

pub mod types;
pub mod paths;
pub mod schema;
pub mod processing;
pub mod vectors;
pub mod vtab;
pub mod utils;

// Re-export main public API
pub use schema::{TwoPassJsonProcessor, InferredSchema, infer_schema_with_capacities, infer_schema_from_file};
pub use types::{InferredJsonType, TempJsonValue, SchemaInferenceConfig};
pub use paths::{VectorPath, ProjectionPath, VectorCapacities};
pub use vtab::{JsonReaderVTab, streaming_json_reader_init};