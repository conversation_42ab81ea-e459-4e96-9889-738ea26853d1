"""
Targeted tests for specific array processing issues.
These tests are designed to isolate and identify exact failure points.
"""

import pytest
import duckdb
import json
import tempfile
import os


class TestTargetedArrayIssues:
    """Targeted tests to identify specific array processing failures."""

    @pytest.fixture
    def duckdb_conn(self):
        """Create a DuckDB connection with the extension loaded."""
        # Enable unsigned extensions
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})

        # Load the extension
        extension_path = "./build/debug/streaming_json_reader.duckdb_extension"
        if not os.path.exists(extension_path):
            pytest.skip(f"Extension not found at {extension_path}")

        try:
            conn.execute(f"LOAD '{extension_path}'")
            print("Extension loaded successfully - table function registered")
        except Exception as e:
            pytest.fail(f"Failed to load extension: {e}")

        return conn

    @pytest.fixture
    def temp_json_file(self):
        """Create a temporary JSON file."""
        fd, path = tempfile.mkstemp(suffix='.json')
        os.close(fd)
        yield path
        os.unlink(path)

    def create_json_file(self, data, file_path):
        """Helper to create JSON file with given data."""
        with open(file_path, 'w') as f:
            json.dump(data, f)

    def test_simple_2d_regular_array(self, duckdb_conn, temp_json_file):
        """Test simplest possible 2D regular array."""
        # [[1, 2], [3, 4]] - perfectly regular
        data = [[[1, 2], [3, 4]]]
        self.create_json_file(data, temp_json_file)
        
        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        print("Simple 2D regular result:", result)
        
        assert len(result) == 1
        row = result[0][0]
        assert len(row) == 2
        assert row[0] == [1, 2]
        assert row[1] == [3, 4]

    def test_simple_2d_irregular_array(self, duckdb_conn, temp_json_file):
        """Test simplest possible 2D irregular array."""
        # [[1, 2], [3]] - one element different
        data = [[[1, 2], [3]]]
        self.create_json_file(data, temp_json_file)
        
        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        print("Simple 2D irregular result:", result)
        
        assert len(result) == 1
        row = result[0][0]
        assert len(row) == 2
        assert row[0] == [1, 2]
        assert row[1] == [3]

    def test_two_rows_regular_3d(self, duckdb_conn, temp_json_file):
        """Test two rows of regular 3D arrays."""
        # [[[1, 2]], [[3, 4]]] - two rows, each with one sub-array of length 2
        data = [[[1, 2]], [[3, 4]]]
        self.create_json_file(data, temp_json_file)
        
        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        print("Two rows regular 3D result:", result)
        
        assert len(result) == 2
        assert result[0][0] == [[1, 2]]
        assert result[1][0] == [[3, 4]]

    def test_two_rows_different_sub_array_counts(self, duckdb_conn, temp_json_file):
        """Test two rows with different numbers of sub-arrays."""
        # Row 0: [[1, 2], [3, 4]] - 2 sub-arrays
        # Row 1: [[5, 6]] - 1 sub-array
        data = [[[1, 2], [3, 4]], [[5, 6]]]
        self.create_json_file(data, temp_json_file)
        
        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        print("Two rows different sub-array counts result:", result)
        
        assert len(result) == 2
        # Row 0 should have 2 sub-arrays
        assert len(result[0][0]) == 2
        assert result[0][0][0] == [1, 2]
        assert result[0][0][1] == [3, 4]
        # Row 1 should have 1 sub-array
        assert len(result[1][0]) == 1
        assert result[1][0][0] == [5, 6]

    def test_two_rows_different_element_counts(self, duckdb_conn, temp_json_file):
        """Test two rows with different element counts in sub-arrays."""
        # Row 0: [[1, 2, 3]] - 1 sub-array with 3 elements
        # Row 1: [[4, 5]] - 1 sub-array with 2 elements
        data = [[[1, 2, 3]], [[4, 5]]]
        self.create_json_file(data, temp_json_file)
        
        result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
        print("Two rows different element counts result:", result)
        
        assert len(result) == 2
        assert result[0][0] == [[1, 2, 3]]
        assert result[1][0] == [[4, 5]]

    def test_offset_calculation_debug(self, duckdb_conn, temp_json_file):
        """Test specifically designed to debug offset calculation."""
        # This should trigger the exact failing case from varying_row_lengths
        # Row 0: 3 sub-arrays, 6 total elements (offsets 0-5)
        # Row 1: 1 sub-array, 2 total elements (should start at offset 6)
        data = [[[1, 2], [3, 4], [5, 6]], [[7, 8]]]
        self.create_json_file(data, temp_json_file)
        
        try:
            result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
            print("Offset calculation debug result:", result)
            
            # If we get here, check the data
            assert len(result) == 2
            
            # Row 0 should be [[1, 2], [3, 4], [5, 6]]
            row0 = result[0][0]
            assert len(row0) == 3
            print(f"Row 0, element 0: {row0[0]} (expected [1, 2])")
            print(f"Row 0, element 1: {row0[1]} (expected [3, 4])")
            print(f"Row 0, element 2: {row0[2]} (expected [5, 6])")
            
            # Row 1 should be [[7, 8]]
            row1 = result[1][0]
            assert len(row1) == 1
            print(f"Row 1, element 0: {row1[0]} (expected [7, 8])")
            
            # Check exact values
            assert row0[0] == [1, 2], f"Row 0, element 0: expected [1, 2], got {row0[0]}"
            assert row0[1] == [3, 4], f"Row 0, element 1: expected [3, 4], got {row0[1]}"
            assert row0[2] == [5, 6], f"Row 0, element 2: expected [5, 6], got {row0[2]}"
            assert row1[0] == [7, 8], f"Row 1, element 0: expected [7, 8], got {row1[0]}"
            
        except Exception as e:
            print(f"EXACT FAILURE POINT: {e}")
            raise

    def test_capacity_lookup_paths(self, duckdb_conn, temp_json_file):
        """Test to understand capacity lookup path issues."""
        # Simple case that should work
        data = [[[1, 2]]]
        self.create_json_file(data, temp_json_file)
        
        try:
            result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
            print("Capacity lookup paths result:", result)
            assert result[0][0] == [[1, 2]]
        except Exception as e:
            print(f"CAPACITY LOOKUP FAILURE: {e}")
            raise

    def test_minimal_4d_array(self, duckdb_conn, temp_json_file):
        """Test minimal 4D array to isolate 4D issues."""
        # [[[[1]]]] - simplest possible 4D
        data = [[[[[1]]]]]
        self.create_json_file(data, temp_json_file)
        
        try:
            result = duckdb_conn.execute(f'SELECT * FROM streaming_json_reader("{temp_json_file}")').fetchall()
            print("Minimal 4D result:", result)
            
            assert len(result) == 1
            array_4d = result[0][0]
            assert len(array_4d) == 1
            array_3d = array_4d[0]
            assert len(array_3d) == 1
            array_2d = array_3d[0]
            assert len(array_2d) == 1
            array_1d = array_2d[0]
            assert array_1d == [1]
            
        except Exception as e:
            print(f"4D ARRAY FAILURE: {e}")
            raise
